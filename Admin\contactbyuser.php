

<?php
       error_reporting(E_ALL);
       ini_set("display_errors", 1);
   
       // Header cho phép truy cập từ mọi nguồn (CORS)
       header("Access-Control-Allow-Origin: *");
       header("Access-Control-Allow-Headers: Content-Type, Authorization");
       header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
       
       
   
       // Kết nối cơ sở dữ liệu
       include '../DbConnect.php';
       $onjDB = new DbConnect();
       $conn = $onjDB->connect();
   
       $method = $_SERVER['REQUEST_METHOD'];
     if ($method == "GET") {
        try {
            $username = $_GET['username'];
            $sql = "SELECT * FROM contact WHERE username = :username";
            $stmt = $conn->prepare($sql);
            
            // Ràng buộc tham số username
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            
            $stmt->execute();
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
            if (empty($notifications)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có thông báo nào cho người dùng này.']);
            } else {
                echo json_encode(['status' => 1, 'notifications' => $notifications]); // Trả về tất cả các bản ghi
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    }else  if ($method == "POST") {
        try {
    
            $data = json_decode(file_get_contents("php://input"), true);
            $descript = $data['descript']; 
            $username = $data['username']; 
            $feedback = $data['feedback'];
           
            $sql = "UPDATE contact SET feedback = :feedback WHERE descript = :descript AND username = :username";
            $stmt = $conn->prepare($sql);
            
            $stmt->bindParam(':feedback', $feedback, PDO::PARAM_STR);
            $stmt->bindParam(':descript', $descript, PDO::PARAM_STR);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
                      
            if ($stmt->execute()) {
                echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
            } else {
                echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    }

?>