<?php 
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Headers: *");
    header('Content-Type: application/json; charset=UTF-8'); // Đ<PERSON>m bảo trả về JSON
    
    include '../DbConnect.php';
    $onjDB = new DbConnect();
    $conn = $onjDB->connect();

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case "GET":
            try {
                $title = $_GET['title'] ?? null; // Lấy giá trị title từ tham số GET
                if ($title === null) {
                    echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số title']);
                    exit;
                }

                // Sử dụng LIKE để tìm kiếm gần đúng
                $sql = "SELECT * FROM product WHERE title LIKE :title";
                $stmt = $conn->prepare($sql);

                // Thêm ký tự % để tìm kiếm gần đúng
                $searchTitle = "%" . $title . "%";
                $stmt->bindParam(':title', $searchTitle, PDO::PARAM_STR);
                $stmt->execute();
                
                $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (empty($listbook)) {
                    echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
                } else {
                    echo json_encode(['status' => 1, 'books' => $listbook]);
                }
            } catch (PDOException $e) {
                // Trả về lỗi dưới dạng JSON
                echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
            }
            break;
    }

?>
