<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $bill = json_decode(file_get_contents('php://input'));
    $errors = [];

   // Kiểm tra cấu trúc dữ liệu đầu vào
if (!isset($bill->quality) || !isset($bill->total) || !isset($bill->idcus)) {
    echo json_encode(['status' => 0, 'message' => '<PERSON><PERSON> liệu không hợp lệ.']);
    exit;
}


  // Kiểm tra số lượng phần tử các mảng có đồng đều không
if (count($bill->quality) !== count($bill->total) || count($bill->quality) !== count($bill->image)) {
    echo json_encode(['status' => 0, 'message' => 'Dữ liệu không hợp lệ.']);
    exit;
}


    // Lặp qua từng phần tử của mảng id để thực hiện chèn dữ liệu
    foreach ($bill->quality as $index => $quality) {
        $stmt = $conn->prepare("INSERT INTO bill(idbill, quality, ship, method, status, total, idcus, image , title , price, created_at) 
                                VALUES (null, :quality, :ship, :method, :status, :total, :idcus, :image , :title , :price , :created_at)");

        // Sử dụng biến tạm để lưu giá trị trước khi gán
        $quality = $bill->quality[$index];  
        $total = $bill->total[$index];      
        $image = $bill->image[$index];      
        $title = $bill->title[$index];      
        $price = $bill->price[$index];      

        // Kiểm tra và xử lý mảng
        $ship = is_array($bill->ship) ? json_encode($bill->ship) : $bill->ship; 
        $method = is_array($bill->method) ? json_encode($bill->method) : $bill->method; 
        $status = is_array($bill->status) ? json_encode($bill->status) : $bill->status; 
        $idcus = $bill->idcus; // Giả sử idcus là giá trị duy nhất
        $createdAt = date('Y-m-d H:i:s');

        // Gán các biến vào câu lệnh
        $stmt->bindParam(':quality', $quality);
        $stmt->bindParam(':ship', $ship);
        $stmt->bindParam(':method', $method);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':total', $total);
        $stmt->bindParam(':idcus', $idcus);
        $stmt->bindParam(':image', $image);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':created_at', $createdAt);

        // Thực thi câu lệnh và kiểm tra lỗi
        if (!$stmt->execute()) {
            $errorInfo = $stmt->errorInfo(); // Lưu vào biến trước khi thêm vào mảng
            $errors[] = ['id' => $id, 'error' => $errorInfo];
        }
    }

    // Trả về kết quả
    echo json_encode([
        'status' => empty($errors) ? 1 : 0,
        'message' => empty($errors) ? 'Thêm vào giỏ hàng thành công.' : 'Lỗi khi thêm sản phẩm vào giỏ hàng.',
        'errors' => $errors,
        'idcus' => $bill->idcus // Trả về idcus
    ]);
}
?>
