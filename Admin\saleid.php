<?php
 error_reporting(E_ALL);
 ini_set("display_errors", 1);

 // Header cho phép truy cập từ mọi nguồn (CORS)
 header("Access-Control-Allow-Origin: http://localhost:3000");
 header("Access-Control-Allow-Headers: Content-Type, Authorization");
 header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 
 header("Access-Control-Allow-Headers: *");
 header("Access-Control-Allow-Origin: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method == "GET") {
    try {
        $idvou = $_GET['idvou'];
        $sql = "SELECT * FROM voucher WHERE idvou = :idvou";
        $stmt = $conn->prepare($sql);
        
        // Bind the idvou to the prepared statement
        $stmt->bindParam(':idvou', $idvou, PDO::PARAM_STR);
        
        $stmt->execute();
        $user = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($user)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có người dùng nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'user' => $user[0]]); // Return only the first user record
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        $idvou = $data['idvou']; 
        $saleof = $data['saleof'];
        $limit = $data['limit'];

        $sql = "UPDATE voucher SET saleof = :saleof, `limit` = :limit WHERE idvou = :idvou";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':saleof', $saleof, PDO::PARAM_STR);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_STR);
        $stmt->bindParam(':idvou', $idvou, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>