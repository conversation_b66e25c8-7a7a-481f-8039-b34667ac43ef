<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS"); // Cho phép các phương thức HTTP
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if($method == 'OPTIONS') {
    // Xử lý preflight request của CORS, phản hồi 200 OK
    http_response_code(200);
    exit();
}

if($method == 'POST' || $method == 'PUT'){
    try {
        // Lấy dữ liệu từ body JSON
        $data = json_decode(file_get_contents("php://input"), true);

        if(isset($data['username'], $data['fullname'], $data['phone'], $data['city'], $data['distric'], $data['ward'], $data['address'])) {
            $idcus = $data['idcus'];
            $username = $data['username'];
            $fullname = $data['fullname'];
            $phone = $data['phone'];
            $city = $data['city'];
            $distric = $data['distric'];
            $ward = $data['ward'];
            $address = $data['address'];

            // Câu lệnh SQL để cập nhật thông tin khách hàng
            $sql = "UPDATE customer SET fullname = :fullname, phone = :phone, city = :city, distric = :distric, ward = :ward, address = :address WHERE idcus = :idcus AND username = :username";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':fullname', $fullname);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':city', $city);
            $stmt->bindParam(':distric', $distric);
            $stmt->bindParam(':ward', $ward);
            $stmt->bindParam(':address', $address);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':idcus', $idcus);

            if ($stmt->execute()) {
                echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
            } else {
                echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
            }
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Thiếu dữ liệu đầu vào.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['status' => 0, 'msg' => 'Yêu cầu không hợp lệ.']);
}
?>
