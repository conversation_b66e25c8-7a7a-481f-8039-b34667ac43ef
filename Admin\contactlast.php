

<?php
       error_reporting(E_ALL);
       ini_set("display_errors", 1);
   
       // Header cho phép truy cập từ mọi nguồn (CORS)
       header("Access-Control-Allow-Origin: *");
       header("Access-Control-Allow-Headers: Content-Type, Authorization");
       header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
       
       
   
       // Kết nối cơ sở dữ liệu
       include '../DbConnect.php';
       $onjDB = new DbConnect();
       $conn = $onjDB->connect();
   
       $method = $_SERVER['REQUEST_METHOD'];
     if($method == 'GET') {
        try {
            $username = $_GET['username'];
            $sql = "SELECT * FROM contact WHERE username = :username ORDER BY id DESC LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
            
            $cus = $stmt->fetch(PDO::FETCH_ASSOC);
    
            if (empty($cus)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có khách hàng nào trong danh sách.']);
            } else {
                echo json_encode(['status' => 1, 'customer' => $cus]);
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    }
    

?>