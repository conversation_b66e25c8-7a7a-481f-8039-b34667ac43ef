<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");


include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        // Query to fetch all books
        $sql = "SELECT * FROM product";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Query to calculate total price
        $sqlTotal = "SELECT SUM(sold) AS total_quality FROM product";
        $stmtTotal = $conn->prepare($sqlTotal);
        $stmtTotal->execute();
        $totalPrice = $stmtTotal->fetch(PDO::FETCH_ASSOC)['total_quality'];

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook, 'total_quality' => $totalPrice]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($_FILES['image']['name'])) {
        $image = $_FILES['image']['name'];
        $image_tmp = $_FILES['image']['tmp_name'];
        $target_dir = $_SERVER['DOCUMENT_ROOT'] . "/bookapp/public/image/";
        $target_dir2 = $_SERVER['DOCUMENT_ROOT'] . "/admin/public/image/";
        $target_file = $target_dir . basename($image);
        $target_file2 = $target_dir2 . basename($image);

        // Move the uploaded file
        if (move_uploaded_file($image_tmp, $target_file)) {
            $image_path =basename($image);
            if (copy($target_file, $target_file2)) {
                // Thành công khi lưu ảnh vào cả hai vị trí
                $response = ['status' => 1, 'message' => 'File uploaded successfully to both locations.'];
            } else {
                $response = ['status' => 0, 'message' => 'Failed to copy file to the second location.'];
            } // Đường dẫn lưu vào database
        } else {
            $response = ['status' => 0, 'message' => 'File upload failed.'];
            echo json_encode($response);
            exit();
        }
    } else {
        $response = ['status' => 0, 'message' => 'Không có file ảnh nào được upload.'];
        echo json_encode($response);
        exit();
    }

    $books = json_decode(file_get_contents('php://input'));
    
    // Insert into the database
    $sql = "INSERT INTO product (id, title, price, priceold, image, sold, sale, type, created_at) 
            VALUES (NULL, :title, :price, :priceold, :image, :sold, :sale, :type, :created_at)";

    $stmt = $conn->prepare($sql);
    $created_at = date('Y-m-d');

    $stmt->bindParam(':title', $_POST['title']);
    $stmt->bindParam(':price', $_POST['price']);
    $stmt->bindParam(':priceold', $_POST['priceold']);
    $stmt->bindParam(':image', $image_path); // Save path in the database
    $stmt->bindParam(':sold', $_POST['sold']);
    $stmt->bindParam(':sale', $_POST['sale']);
    $stmt->bindParam(':type', $_POST['type']);
    $stmt->bindParam(':created_at', $created_at);

    if ($stmt->execute()) {
        $idbook = $conn->lastInsertId();
        $response = [
            'status' => 1,
            'message' => 'Thêm thông tin sách thành công.',
            'id' => $idbook
        ];
    } else {
        $response = ['status' => 0, 'message' => 'Thêm thông tin sách thất bại.'];
    }

    echo json_encode($response);
}else if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    parse_str(file_get_contents("php://input"), $_PUT);
    
    $id = $data['id']; 
    $title = $_PUT['title'] ?? null;
    $price = $_PUT['price'] ?? null;
    $priceold = $_PUT['priceold'] ?? null;
    $sold = $_PUT['sold'] ?? null;
    $sale = $_PUT['sale'] ?? null;
    $type = $_PUT['type'] ?? null;
    $created_at = date('Y-m-d');
    $image_path = null;

    // Check if image is provided in the files
    if (!empty($_FILES['image']['name'])) {
        $image = $_FILES['image']['name'];
        $image_tmp = $_FILES['image']['tmp_name'];
        $target_dir = $_SERVER['DOCUMENT_ROOT'] . "/bookapp/public/image/";
        $target_dir2 = $_SERVER['DOCUMENT_ROOT'] . "/admin/public/image/";
        $target_file = $target_dir . basename($image);
        $target_file2 = $target_dir2 . basename($image);

        // Move the uploaded file to both locations
        if (move_uploaded_file($image_tmp, $target_file)) {
            $image_path = basename($image);
            if (!copy($target_file, $target_file2)) {
                $response = ['status' => 0, 'message' => 'Failed to copy file to the second location.'];
                echo json_encode($response);
                exit();
            }
        } else {
            $response = ['status' => 0, 'message' => 'File upload failed.'];
            echo json_encode($response);
            exit();
        }
    }

    // SQL query for updating the book
    if ($id) {
        $sql = "UPDATE product SET title = :title, price = :price, priceold = :priceold, sold = :sold, sale = :sale, `type` = :type, created_at = :created_at";
        if ($image_path) {
            $sql .= ", image = :image"; // Add image field to the update query if image is provided
        }
        $sql .= " WHERE id = :id";

        $stmt = $conn->prepare($sql);
        
        // Bind parameters
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':priceold', $priceold);
        $stmt->bindParam(':sold', $sold);
        $stmt->bindParam(':sale', $sale);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':created_at', $created_at);
        if ($image_path) {
            $stmt->bindParam(':image', $image_path); // If image is present, bind it
        }
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            $response = ['status' => 1, 'message' => 'Cập nhật thông tin sách thành công.'];
        } else {
            $response = ['status' => 0, 'message' => 'Cập nhật thông tin sách thất bại.'];
        }
    } else {
        $response = ['status' => 0, 'message' => 'Không tìm thấy ID sách.'];
    }

    echo json_encode($response);
}else if($method == "DELETE") {
    try {
        $id = $_GET['id'];

        // Kiểm tra xem id có được cung cấp không
        if (empty($id)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM product WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}



?>