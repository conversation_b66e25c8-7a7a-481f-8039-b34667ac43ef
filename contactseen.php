

<?php
       error_reporting(E_ALL);
       ini_set("display_errors", 1);
   
       // Header cho phép truy cập từ mọi nguồn (CORS)
       header("Access-Control-Allow-Origin: *");
       header("Access-Control-Allow-Headers: Content-Type, Authorization");
       header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
       
       
   
       // Kết nối cơ sở dữ liệu
       include 'DbConnect.php';
       $onjDB = new DbConnect();
       $conn = $onjDB->connect();
   
       $method = $_SERVER['REQUEST_METHOD'];
       if ($method == "POST") {
        $cartmusic = json_decode(file_get_contents('php://input'));

        $sql = "INSERT INTO contact(id, descript, username, created_at) 
                VALUES(null, :descript, :username, :created_at)";
        $stmt = $conn->prepare($sql);

        $created_at = date('Y-m-d');

        $stmt->bindParam(':descript', $cartmusic->descript);
        $stmt->bindParam(':username', $cartmusic->username);
        $stmt->bindParam(':created_at', $created_at);

        if ($stmt->execute()) {
            $response = ['status' => 1, 'message' => 'Thêm vào giỏ hàng thành công.'];
        } else {
            $response = ['status' => 0, 'message' => 'Thêm vào giỏ hàng thất bại.'];
        }

        echo json_encode($response);
    }
?>