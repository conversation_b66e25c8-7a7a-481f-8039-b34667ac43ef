<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($method === "POST") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $username = $data['username']; 
        $oldPass = $data['oldPass']; 
        $newPass = $data['pass'];

        $sql = "SELECT pass FROM user WHERE username = :username";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $oldPass === $result['pass']) { // Kiểm tra mật khẩu cũ
            // Cập nhật mật khẩu mới
            $sql = "UPDATE user SET pass = :pass WHERE username = :username";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':pass', $newPass, PDO::PARAM_STR);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            if ($stmt->execute()) {
                echo json_encode(['status' => 1, 'msg' => 'Cập nhật mật khẩu thành công.']);
            } else {
                echo json_encode(['status' => 0, 'msg' => 'Cập nhật mật khẩu thất bại.']);
            }
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Mật khẩu cũ không đúng.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>
