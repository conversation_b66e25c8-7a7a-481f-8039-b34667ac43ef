

<?php
       error_reporting(E_ALL);
       ini_set("display_errors", 1);
   
       // Header cho phép truy cập từ mọi nguồn (CORS)
       header("Access-Control-Allow-Origin: *");
       header("Access-Control-Allow-Headers: Content-Type, Authorization");
       header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
       
       
   
       // Kết nối cơ sở dữ liệu
       include 'DbConnect.php';
       $onjDB = new DbConnect();
       $conn = $onjDB->connect();
   
       $method = $_SERVER['REQUEST_METHOD'];
       if ($method == "POST") {
        $cartmusic = json_decode(file_get_contents('php://input'));

        $sql = "INSERT INTO contact(id, title, descript, username, created_at) 
                VALUES(null, :title, :descript, :username, :created_at)";
        $stmt = $conn->prepare($sql);

        $created_at = date('Y-m-d');

        $stmt->bindParam(':title', $cartmusic->title);
        $stmt->bindParam(':descript', $cartmusic->descript);
        $stmt->bindParam(':username', $cartmusic->username);
        $stmt->bindParam(':created_at', $created_at);

        if ($stmt->execute()) {
            $response = ['status' => 1, 'message' => 'Thêm vào giỏ hàng thành công.'];
        } else {
            $response = ['status' => 0, 'message' => 'Thêm vào giỏ hàng thất bại.'];
        }

        echo json_encode($response);
    }else if ($method == "GET") {
        try {
            $username = $_GET['username'];
            $sql = "SELECT * FROM contact WHERE username = :username";
            $stmt = $conn->prepare($sql);
            
            // Ràng buộc tham số username
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            
            $stmt->execute();
            $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
            if (empty($notifications)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có thông báo nào cho người dùng này.']);
            } else {
                echo json_encode(['status' => 1, 'notifications' => $notifications]); // Trả về tất cả các bản ghi
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    }

?>