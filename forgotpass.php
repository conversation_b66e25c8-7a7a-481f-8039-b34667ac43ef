<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($method === "POST") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $Email = $data['Email']; 
        $newPass = $data['pass'];

        $sql = "SELECT email FROM user WHERE email = :email";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':email', $Email, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $Email === $result['email']) { // Verify email
            // Update the new password directly
            $sql = "UPDATE user SET pass = :pass WHERE email = :email";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':pass', $newPass, PDO::PARAM_STR);
            $stmt->bindParam(':email', $Email, PDO::PARAM_STR); // Correct variable here
            if ($stmt->execute()) {
                echo json_encode(['status' => 1, 'msg' => 'Cập nhật mật khẩu thành công.']);
            } else {
                echo json_encode(['status' => 0, 'msg' => 'Cập nhật mật khẩu thất bại.']);
            }
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Email không đúng.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>
