<?php 
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method == "GET") {
    try {
        $username = $_GET['username'];
        $sql = "SELECT * FROM notifycation WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        // Ràng buộc tham số username
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        $stmt->execute();
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($notifications)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có thông báo nào cho người dùng này.']);
        } else {
            echo json_encode(['status' => 1, 'notifications' => $notifications]); // Trả về tất cả các bản ghi
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>

