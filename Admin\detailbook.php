<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);

// CORS headers
header("Access-Control-Allow-Origin: http://localhost:3000"); // Allow requests from this origin
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization"); // Allow specific headers
header('Content-Type: application/json; charset=UTF-8'); // Ensure the response is JSON encoded
header("Access-Control-Allow-Origin: *"); // Allow requests from this origin


// Xử lý yêu cầu preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0); // Thoát ngay khi xử lý xong yêu cầu OPTIONS
}

// Kết nối đến cơ sở dữ liệu
include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

// Ki<PERSON>m tra phương thức yêu cầu
$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    if (isset($_GET['id'])) {
        $id = $_GET['id'];
        try {
            // Sử dụng prepared statement để tránh SQL injection
            $sql = "SELECT product.*, detailmusic.*
                    FROM product
                    INNER JOIN detailmusic ON product.id = detailmusic.id
                    WHERE product.id = :id";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $bookDetail = $stmt->fetch(PDO::FETCH_ASSOC);

            if (empty($bookDetail)) {
                echo json_encode(['status' => 0, 'msg' => 'Không tìm thấy chi tiết  nhạc cụ.']);
            } else {
                echo json_encode(['status' => 1, 'book' => $bookDetail]);
            }
        } catch (PDOException $e) {
            // Trả về lỗi dưới dạng JSON
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số id.']);
    }
} else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if image is uploaded
    if (!empty($_FILES['imagedt']['name'])) {
        $image = $_FILES['imagedt']['name'];
        $image_tmp = $_FILES['imagedt']['tmp_name'];
        $target_dir = $_SERVER['DOCUMENT_ROOT'] . "/bookapp/public/image/";
        $target_dir2 = $_SERVER['DOCUMENT_ROOT'] . "/admin/public/image/";
        $target_file = $target_dir . basename($image);
        $target_file2 = $target_dir2 . basename($image);

        // Check for file upload errors
        if ($_FILES['imagedt']['error'] != UPLOAD_ERR_OK) {
            $response = ['status' => 0, 'message' => 'File upload error: ' . $_FILES['imagedt']['error']];
            echo json_encode($response);
            exit();
        }

        // Move the uploaded file to the target directory
        if (move_uploaded_file($image_tmp, $target_file)) {
            $image_path = basename($image);
            
            // Copy the file to the second location
            if (copy($target_file, $target_file2)) {
                // Success: file uploaded to both locations
                $response = ['status' => 1, 'message' => 'File uploaded successfully to both locations.'];
            } else {
                // Failed to copy to the second location
                $response = ['status' => 0, 'message' => 'Failed to copy file to the second location.'];
            }
        } else {
            // File upload failed
            $response = ['status' => 0, 'message' => 'File upload failed.'];
            echo json_encode($response);
            exit();
        }
    } else {
        // No image uploaded
        $response = ['status' => 0, 'message' => 'No image file uploaded.'];
        echo json_encode($response);
        exit();
    }

    // Get the book details from the request body (JSON)
    $books = json_decode(file_get_contents('php://input'));

    // Prepare the SQL query to insert the book details into the database
    $sql = "INSERT INTO detailmusic 
                (detailid, imagedt, supplier, nhaxuatban, author, hinhthuc, namxb, ngonngu, trongluong, size, sotrang, mota, id, created_at) 
            VALUES 
                (NULL, :imagedt, :supplier, :nhaxuatban, :author, :hinhthuc, :namxb, :ngonngu, :trongluong, :size, :sotrang, :mota, :id, :created_at)";

    // Prepare the statement
    $stmt = $conn->prepare($sql);
    $created_at = date('Y-m-d'); // Get the current date

    // Bind the form parameters to the SQL query
    $stmt->bindParam(':supplier', $_POST['supplier']);
    $stmt->bindParam(':nhaxuatban', $_POST['nhaxuatban']);
    $stmt->bindParam(':author', $_POST['author']);
    $stmt->bindParam(':imagedt', $image_path); // Save the image path in the database
    $stmt->bindParam(':hinhthuc', $_POST['hinhthuc']);
    $stmt->bindParam(':namxb', $_POST['namxb']);
    $stmt->bindParam(':ngonngu', $_POST['ngonngu']);
    $stmt->bindParam(':trongluong', $_POST['trongluong']);
    $stmt->bindParam(':size', $_POST['size']);
    $stmt->bindParam(':sotrang', $_POST['sotrang']);
    $stmt->bindParam(':mota', $_POST['mota']);
    $stmt->bindParam(':id', $_POST['id']); // The ID of the book
    $stmt->bindParam(':created_at', $created_at);

    // Execute the query
    if ($stmt->execute()) {
        $idbook = $conn->lastInsertId(); // Get the last inserted ID
        $response = [
            'status' => 1,
            'message' => 'Book details added successfully.',
            'detailid' => $idbook
        ];
    } else {
        // Failure: insertion failed
        $response = ['status' => 0, 'message' => 'Failed to add book details.'];
    }

    // Return the response as JSON
    echo json_encode($response);
}else if($method == "DELETE") {
    try {
        $detailid = $_GET['detailid'];

        // Kiểm tra xem detailid có được cung cấp không
        if (empty($detailid)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM detailmusic WHERE detailid = :detailid";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':detailid', $detailid);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>
