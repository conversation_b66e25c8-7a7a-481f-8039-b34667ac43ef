<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($method === "GET") {
    try {
        $sql = "SELECT SUM(total) AS total_sum, SUM(quality) AS total_quantity FROM bill WHERE status != 'Đã hủy'";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['total_sum'] !== null && $result['total_quantity'] !== null) {
            echo json_encode([
                'status' => 1,
                'total_sum' => $result['total_sum'],
                'total_quantity' => $result['total_quantity']
            ]);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}


?>
