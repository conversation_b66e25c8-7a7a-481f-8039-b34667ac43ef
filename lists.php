<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: *");
header("Access-Control-Allow-Credentials: true");


include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
     
    case "GET":
        try {
            $sql = "SELECT * FROM product";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($listbook)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
            } else {
                echo json_encode(['status' => 1, 'books' => $listbook]);
            }
        } catch (PDOException $e) {
            // Trả về lỗi dưới dạng JSON
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
        break;
}
?>
