<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $username = $_GET['username'];
        $sql = "SELECT * FROM user WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        // Bind the username to the prepared statement
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        $stmt->execute();
        $user = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($user)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có người dùng nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'user' => $user[0]]); // Return only the first user record
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method === "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        
        $username = $data['username']; 
        $fullname = $data['fullname'];
        $phone = $data['phone'];
        $email = $data['email'];
        $pass = $data['pass'];

        $sql = "UPDATE user SET fullname = :fullname, phone = :phone, email = :email, pass = :pass WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':fullname', $fullname, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':pass', $pass, PDO::PARAM_STR);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>
