<?php
   error_reporting(E_ALL);
   ini_set("display_errors", 1);

   // Header cho phép truy cập từ mọi nguồn (CORS)
   header("Access-Control-Allow-Origin: http://localhost:3000");
   header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Access-Control-Allow-Origin: *");

   header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method == "GET") {
    try {
        $username = $_GET['username'];
        $sql = "SELECT * FROM user WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        // Bind the username to the prepared statement
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        $stmt->execute();
        $user = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($user)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có người dùng nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'user' => $user[0]]); // Return only the first user record
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        
        $username = $data['username']; 
        $fullname = $data['fullname'];
        $phone = $data['phone'];
        $email = $data['email'];
        $address = $data['address'];
        $pass = $data['pass'];

        $sql = "UPDATE user SET fullname = :fullname, `address` = :address , phone = :phone, email = :email, pass = :pass WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':fullname', $fullname, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':address', $address, PDO::PARAM_STR);
        $stmt->bindParam(':pass', $pass, PDO::PARAM_STR);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if($method == "DELETE") {
    try {
        $username = $_GET['username'];

        // Kiểm tra xem username có được cung cấp không
        if (empty($username)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM user WHERE username = :username";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':username', $username);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

 
?>
