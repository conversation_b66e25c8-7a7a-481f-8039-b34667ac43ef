<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $sql = "SELECT * FROM contact";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        $id = $data['id']; 
        $feedback = $data['feedback'];
       
        $sql = "UPDATE contact SET feedback = :feedback WHERE id = :id";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':feedback', $feedback, PDO::PARAM_STR);
      
        $stmt->bindParam(':id', $id, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if($method == "DELETE") {
    try {
        $id = $_GET['id'];

        // Kiểm tra xem username có được cung cấp không
        if (empty($id)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM contact WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>