<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($method === "GET") {
    if (isset($_GET['id'])) {
        $id = $_GET['id'];
        try {
            $sql = "SELECT * FROM product WHERE id = :id";
            $stmt = $conn->prepare($sql);
            
            // Use PDO::PARAM_INT if the id is an integer
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            
            $stmt->execute();
            $bookDetail = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single record

            if (empty($bookDetail)) {
                echo json_encode(['status' => 0, 'msg' => 'Không tìm thấy sách.']);
            } else {
                echo json_encode(['status' => 1, 'book' => $bookDetail]); // Return only one book
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số id.']);
    }
}


?>