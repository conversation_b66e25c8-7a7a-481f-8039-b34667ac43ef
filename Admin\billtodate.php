<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($method === "GET") {
    try {
        $dateFrom = isset($_GET['dateFrom']) ? $_GET['dateFrom'] : null;
        $dateTo = isset($_GET['dateTo']) ? $_GET['dateTo'] : null;

        // Prepare the SQL query with optional date filtering and total calculations
        $sql = "SELECT *, 
                       (SELECT SUM(total) FROM bill WHERE status != 'Đã hủy' AND (:dateFrom IS NULL OR updated_at >= :dateFrom) AND (:dateTo IS NULL OR updated_at <= :dateTo)) AS total_sum,
                       (SELECT SUM(quality) FROM bill WHERE status != 'Đã hủy' AND (:dateFrom IS NULL OR updated_at >= :dateFrom) AND (:dateTo IS NULL OR updated_at <= :dateTo)) AS total_quantity
                FROM bill
                WHERE status != 'Đã hủy'";

        // Add date filtering conditions if both dates are provided
        if ($dateFrom && $dateTo) {
            $sql .= " AND updated_at BETWEEN :dateFrom AND :dateTo";
        }

        $stmt = $conn->prepare($sql);

        // Bind the date parameters if provided
        if ($dateFrom && $dateTo) {
            $stmt->bindParam(':dateFrom', $dateFrom);
            $stmt->bindParam(':dateTo', $dateTo);
        }

        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($results) {
            echo json_encode([
                'status' => 1,
                'data' => $results,
                'total_sum' => $results[0]['total_sum'],
                'total_quantity' => $results[0]['total_quantity']
            ]);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}





?>
