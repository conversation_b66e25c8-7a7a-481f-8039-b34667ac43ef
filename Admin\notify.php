<?php
 error_reporting(E_ALL);
 ini_set("display_errors", 1);

 // Header cho phép truy cập từ mọi nguồn (CORS)
 header("Access-Control-Allow-Headers: Content-Type, Authorization");
 header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 
 header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $sql = "SELECT * FROM notifycation";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có thông báo nào nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if($method == "DELETE") {
    try {
        $id = $_GET['id'];

        // Kiểm tra xem username có được cung cấp không
        if (empty($id)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM notifycation WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    $cartmusic = json_decode(file_get_contents('php://input'));

    $sql = "INSERT INTO notifycation(id, title, daynow, descript, username, detailid, created_at) 
            VALUES(null, :title, :daynow, :descript, :username, :detailid, :created_at)";
    $stmt = $conn->prepare($sql);

    $created_at = date('Y-m-d');
    $daynow = date('Y-m-d');
    $detailId = 4;

    $stmt->bindParam(':title', $cartmusic->title);
    $stmt->bindParam(':daynow', $daynow);
    $stmt->bindParam(':username', $cartmusic->username);
    $stmt->bindParam(':detailid', $detailId);
    $stmt->bindParam(':descript', $cartmusic->descript);
    $stmt->bindParam(':created_at', $created_at);

    if ($stmt->execute()) {
        $response = ['status' => 1, 'message' => 'Thêm thông báo thành công.'];
    } else {
        $response = ['status' => 0, 'message' => 'Thêm vào giỏ hàng thất bại.'];
    }

    echo json_encode($response);
}

?>