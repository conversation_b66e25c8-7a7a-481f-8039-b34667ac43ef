<?php
   error_reporting(E_ALL);
   ini_set("display_errors", 1);
   
   header("Access-Control-Allow-Origin: *");
   header("Access-Control-Allow-Headers: Content-Type, Authorization");
   header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");
   
   include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if($method == 'GET') {
    try {
        $username = $_GET['username'];
        $sql = "SELECT * FROM voucher WHERE username = :username";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $cus = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($cus)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có khách hàng nào trong danh sách.']);
        } else {
            // Corrected response, using $cus instead of $listbook
            echo json_encode(['status' => 1, 'customer' => $cus]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>