<?php 
error_reporting(E_ALL);
ini_set("display_errors", 1);

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS");

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method == "POST") {
    $customer = json_decode(file_get_contents('php://input'));
    
    $username = $customer->username; 

    $sql = "INSERT INTO customer(idcus, fullname, phone, city, distric, ward, address, username, created_at) 
            VALUES(null, :fullname, :phone, :city, :distric, :ward, :address, :username, :created_at)";
    
    $stmt = $conn->prepare($sql);
    $created_at = date('Y-m-d');

    $stmt->bindParam(':fullname', $customer->fullname);
    $stmt->bindParam(':phone', $customer->phone);
    $stmt->bindParam(':city', $customer->city);
    $stmt->bindParam(':distric', $customer->distric);
    $stmt->bindParam(':ward', $customer->ward);
    $stmt->bindParam(':address', $customer->address);
    $stmt->bindParam(':username', $username, PDO::PARAM_STR);
    $stmt->bindParam(':created_at', $created_at);

    if ($stmt->execute()) {
        // Lấy idcus vừa chèn
        $idcus = $conn->lastInsertId(); 
        $response = [
            'status' => 1, 
            'message' => 'Thêm thông tin khách hàng thành công.',
            'idcus' => $idcus // Thêm idcus vào phản hồi
        ];
    } else {
        $response = ['status' => 0, 'message' => 'Thêm thông tin khách hàng thất bại.'];
    }

    echo json_encode($response);
}
else if($method == 'GET') {
    try {
        $username = $_GET['username'];
        $sql = "SELECT * FROM customer WHERE username = :username";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        $cus = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($cus)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có khách hàng nào trong danh sách.']);
        } else {
            // Corrected response, using $cus instead of $listbook
            echo json_encode(['status' => 1, 'customer' => $cus]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>