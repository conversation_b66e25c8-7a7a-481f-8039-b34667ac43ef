<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 
header("Access-Control-Allow-Methods: PUT, POST, GET, OPTIONS"); // Cho phép các phương thức

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kiểm tra ID sách
    $id = $_POST['id'] ?? null;
    if (!$id) {
        echo json_encode(['status' => 0, 'message' => 'Không tìm thấy ID sách.']);
        exit();
    }

    // Lấy các thông tin sách
    $title = $_POST['title'] ?? null;
    $price = $_POST['price'] ?? null;
    $priceold = $_POST['priceold'] ?? null;
    $sold = $_POST['sold'] ?? null;
    $sale = $_POST['sale'] ?? null;
    $type = $_POST['type'] ?? null;
    $created_at = date('Y-m-d');
    $image_path = null;

    // Kiểm tra và xử lý ảnh
    if (!empty($_FILES['image']['name'])) {
        $image = $_FILES['image']['name'];
        $image_tmp = $_FILES['image']['tmp_name'];
        $target_dir = $_SERVER['DOCUMENT_ROOT'] . "/bookapp/public/image/";
        $target_dir2 = $_SERVER['DOCUMENT_ROOT'] . "/admin/public/image/";
        $target_file = $target_dir . basename($image);
        $target_file2 = $target_dir2 . basename($image);

        // Di chuyển ảnh đến cả hai thư mục
        if (move_uploaded_file($image_tmp, $target_file)) {
            $image_path = basename($image);
            if (!copy($target_file, $target_file2)) {
                echo json_encode(['status' => 0, 'message' => 'Không thể sao chép ảnh đến thư mục thứ hai.']);
                exit();
            }
        } else {
            echo json_encode(['status' => 0, 'message' => 'Tải ảnh lên không thành công.']);
            exit();
        }
    }

    // Câu truy vấn SQL để cập nhật sách
    $sql = "UPDATE product SET title = :title, price = :price, priceold = :priceold, sold = :sold, sale = :sale, `type` = :type, created_at = :created_at";
    if ($image_path) {
        $sql .= ", image = :image"; // Thêm trường image nếu có ảnh
    }
    $sql .= " WHERE id = :id";

    $stmt = $conn->prepare($sql);
    
    // Gán giá trị
    $stmt->bindParam(':title', $title);
    $stmt->bindParam(':price', $price);
    $stmt->bindParam(':priceold', $priceold);
    $stmt->bindParam(':sold', $sold);
    $stmt->bindParam(':sale', $sale);
    $stmt->bindParam(':type', $type);
    $stmt->bindParam(':created_at', $created_at);
    if ($image_path) {
        $stmt->bindParam(':image', $image_path); // Gán giá trị ảnh nếu có
    }
    $stmt->bindParam(':id', $id);

    // Thực thi và trả về phản hồi
    if ($stmt->execute()) {
        $response = ['status' => 1, 'message' => 'Cập nhật thông tin sách thành công.'];
    } else {
        $response = ['status' => 0, 'message' => 'Cập nhật thông tin sách thất bại.'];
    }

    echo json_encode($response);
}




?>