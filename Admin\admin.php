<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
switch ($method) {
    case "GET":{
        if (isset($_GET['username']) && isset($_GET['pass'])) {
            $username = $_GET['username'];
            $pass = $_GET['pass'];

            // Kiểm tra thông tin đăng nhập
            $checkSql = "SELECT * FROM loginadmin WHERE username = :username";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->bindParam(':username', $username);
            $checkStmt->execute();

            if ($checkStmt->rowCount() > 0) {
                $row = $checkStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($row['pass'] === $pass) {
                    $response = [
                        'status' => 1,
                        'msg' => 'Đăng nhập thành công.',
                        'token' => 'dummy-token' // Thay bằng token thực tế nếu có
                    ];
                } else {
                    $response = ['status' => 0, 'msg' => 'Mật khẩu không đúng.'];
                }
            } else {
                $response = ['status' => 0, 'msg' => 'Tên người dùng không tồn tại.'];
            }
        } else {
            $response = ['status' => 0, 'msg' => 'Vui lòng cung cấp tên người dùng và mật khẩu.'];
        }

        echo json_encode($response);
        break;
    }
}
