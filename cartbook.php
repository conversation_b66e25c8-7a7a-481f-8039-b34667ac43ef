<?php
    // <PERSON><PERSON><PERSON> thị tất cả lỗi (chỉ dùng khi phát triển)
    error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: *");
header("Access-Control-Allow-Credentials: true");

    

    // Kết nối cơ sở dữ liệu
    include 'DbConnect.php';
    $onjDB = new DbConnect();
    $conn = $onjDB->connect();

    $method = $_SERVER['REQUEST_METHOD'];

    if ($method == "POST") {
        $cartmusic = json_decode(file_get_contents('php://input'));

        $sql = "INSERT INTO cartmusic(id, image_cart, title, price, priceold, username, created_at, updated_at) 
                VALUES(null, :image_cart, :title, :price, :priceold, :username, :created_at, null)";
        $stmt = $conn->prepare($sql);

        $created_at = date('Y-m-d');

        $stmt->bindParam(':image_cart', $cartmusic->image_cart);
        $stmt->bindParam(':title', $cartmusic->title);
        $stmt->bindParam(':price', $cartmusic->price);
        $stmt->bindParam(':priceold', $cartmusic->priceold);
        $stmt->bindParam(':username', $cartmusic->username);
        $stmt->bindParam(':created_at', $created_at);

        if ($stmt->execute()) {
            $response = ['status' => 1, 'message' => 'Thêm vào giỏ hàng thành công.'];
        } else {
            $response = ['status' => 0, 'message' => 'Thêm vào giỏ hàng thất bại.'];
        }

        echo json_encode($response);
    }else if($method == 'GET'){
        try {
            $username = $_GET['username'];

            $sql = "SELECT * FROM cartmusic WHERE username = :username";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':username', $username);
            $stmt->execute();
    
            $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
            if (empty($listbook)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
            } else {
                echo json_encode(['status' => 1, 'books' => $listbook]);
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
    }else if($method == "DELETE") {
       try {
        $id = $_GET['id'];

        // Kiểm tra xem ID có được cung cấp hay không
        if (empty($id)) {
            echo json_encode(['status' => 0, 'msg' => 'ID sách không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM cartmusic WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':id', $id);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa sách thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa sách']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
    }
    
    
    
    
?>
