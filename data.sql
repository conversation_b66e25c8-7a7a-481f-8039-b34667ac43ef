-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 29, 2024 at 04:05 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `book`
--

-- --------------------------------------------------------

--
-- Table structure for table `bill`
--

CREATE TABLE `bill` (
  `idbill` int(11) NOT NULL,
  `quality` int(11) NOT NULL,
  `status` varchar(200) NOT NULL,
  `ship` double NOT NULL,
  `method` varchar(100) NOT NULL,
  `total` double NOT NULL,
  `idcus` int(11) NOT NULL,
  `image` char(40) NOT NULL,
  `title` varchar(500) NOT NULL,
  `price` double NOT NULL,
  `username` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bill`
--

INSERT INTO `bill` (`idbill`, `quality`, `status`, `ship`, `method`, `total`, `idcus`, `image`, `title`, `price`, `username`, `created_at`, `updated_at`) VALUES
(102, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 634200, 7, 'book4.webp', 'Combo 6 sách kinh tế', 664200, '', '2024-10-27 21:09:09', '2024-10-28 03:09:09'),
(103, 1, 'Đã đặt', 30000, '', 64500, 7, 'book7.webp', 'Nói Chuyện Là Bản Năng, Giữ Miệng Là Tu Dưỡng, Im Lặng Là Trí Tuệ (Tái Bản)', 94500, '', '2024-10-27 21:51:56', '2024-10-28 03:51:56'),
(104, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 64500, 7, 'book7.webp', 'Nói Chuyện Là Bản Năng, Giữ Miệng Là Tu Dưỡng, Im Lặng Là Trí Tuệ (Tái Bản)', 94500, '', '2024-10-27 21:54:59', '2024-10-28 03:54:59'),
(105, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 152700, 7, 'book8.webp', 'Giải Thích Ngữ Pháp Tiếng Anh (Tái Bản 2024)', 160200, '', '2024-10-27 22:12:41', '2024-10-28 04:12:41'),
(106, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 87000, 7, 'book7.webp', 'Nói Chuyện Là Bản Năng, Giữ Miệng Là Tu Dưỡng, Im Lặng Là Trí Tuệ (Tái Bản)', 94500, '', '2024-10-27 22:12:41', '2024-10-28 04:12:41'),
(107, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 53250, 7, 'book5.webp', 'Đất Rừng Phương Nam (Tái Bản)', 60750, '', '2024-10-27 22:12:41', '2024-10-28 04:12:41'),
(108, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 346084, 7, 'book3.webp', 'Combo Sách Thế Giới Phẳng + Từ Tốt Đến Vĩ Đại (Bộ 2 Cuốn)', 353584, '', '2024-10-27 22:12:41', '2024-10-28 04:12:41'),
(109, 1, 'Đã đặt', 30000, 'Thanh toán khi nhân hàng', 130200, 7, 'book8.webp', 'Giải Thích Ngữ Pháp Tiếng Anh (Tái Bản 2024)', 160200, '', '2024-10-27 22:22:20', '2024-10-28 04:22:20');

-- --------------------------------------------------------

--
-- Table structure for table `books`
--

CREATE TABLE `books` (
  `id` int(11) NOT NULL,
  `title` varchar(300) NOT NULL,
  `price` double NOT NULL,
  `priceold` double NOT NULL,
  `image` char(255) NOT NULL,
  `sold` int(10) NOT NULL,
  `sale` varchar(20) NOT NULL,
  `type` varchar(200) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `update_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `books`
--

INSERT INTO `books` (`id`, `title`, `price`, `priceold`, `image`, `sold`, `sale`, `type`, `created_at`, `update_at`) VALUES
(1, 'Destination B1 - Grammar And Vocabulary with Answer Key', 260000, 325000, 'book2.webp', 988, '20%', 'Tiếng Anh', '2024-10-11 14:10:32', '2024-10-11 14:10:32'),
(3, 'Combo Sách Thế Giới Phẳng + Từ Tốt Đến Vĩ Đại (Bộ 2 Cuốn)', 353584, 440000, 'book3.webp', 85, '19%', 'Kinh Tế', '2024-10-12 01:04:53', '2024-10-12 01:04:53'),
(4, 'Combo 6 sách kinh tế', 664200, 800000, 'book4.webp', 500, '20%', 'Kinh Tế', '2024-10-12 01:28:44', '2024-10-12 01:28:44'),
(5, 'Đất Rừng Phương Nam (Tái Bản)', 60750, 81000, 'book5.webp', 1000, '25%', 'Thiếu Nhi', '2024-10-12 01:28:44', '2024-10-12 01:28:44'),
(9, 'Sống Mãi Với Thủ Đô (Tái Bản 2024)', 135000, 150000, 'book6.webp', 20, '10%', 'Thiếu Nhi', '2024-10-12 01:36:52', '2024-10-12 01:36:52'),
(10, 'Nói Chuyện Là Bản Năng, Giữ Miệng Là Tu Dưỡng, Im Lặng Là Trí Tuệ (Tái Bản)', 94500, 189000, 'book7.webp', 43, '50%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-12 01:40:48', '2024-10-12 01:40:48'),
(11, 'Giải Thích Ngữ Pháp Tiếng Anh (Tái Bản 2024)', 160200, 220000, 'book8.webp', 59, '27%', 'Tiếng Anh', '2024-10-12 01:47:36', '2024-10-12 01:47:36'),
(12, 'Con Đường Chẳng Mấy Ai Đi', 115500, 165000, 'book9.webp', 55, '30%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-12 01:50:33', '2024-10-12 01:50:33'),
(13, 'Người Bà Tài Giỏi Vùng Saga', 96000, 128000, 'book10.webp', 79, '25%', 'Văn Học', '2024-10-12 01:52:56', '2024-10-12 01:52:56'),
(14, 'Dưới Những Lớp Sóng Thời Gian - Bìa Cứng', 261000, 290000, 'book11.webp', 2700, '10%', 'Văn Học', '2024-10-12 01:54:19', '2024-10-12 01:54:19'),
(15, 'Tô Bình Yên Vẽ Hạnh Phúc (Tái Bản 2022)', 66880, 88000, 'book12.webp', 1700, '27%', 'Tô màu', '2024-10-12 01:57:54', '2024-10-12 01:57:54'),
(16, 'Cùng Bạn Trưởng Thành - Tô Màu Cuộc Sống', 74250, 99000, 'book13.webp', 102, '25%', 'Tô Màu', '2024-10-12 01:59:21', '2024-10-12 01:59:21'),
(17, '\"Chém\" Tiếng Anh Không Cần Động Não', 121680, 169000, 'book14.webp', 59, '28%', 'Tiếng Anh', '2024-10-12 02:02:43', '2024-10-12 02:02:43'),
(18, 'Vũ Khí Kinh Tế - Sự Trỗi Dậy Của Các Biện Pháp Trừng Phạt Kinh Tế Như Một Công Cụ Chiến Tranh Hiện Đại (Tái Bản 2024)', 169350, 255000, 'book15.webp', 25, '23%', 'Kinh Tế', '2024-10-12 02:04:20', '2024-10-12 02:04:20'),
(19, 'Tấm Thiệp Cảm Xúc', 72200, 95000, 'book16.webp', 45, '24%', 'Văn Học', '2024-10-12 02:06:09', '2024-10-12 02:06:09'),
(20, 'Sách Lược Đầu Tư Của W Buffett - Tổng Kết Lại Một Cách Sinh Động Bí Quyết Đầu Tư Của Huyền Thoại Cổ Phiếu W Buffett ( Tái Bản 2021)', 75000, 100000, 'book70.webp', 21, '25%', 'Kinh Tế', '2024-10-22 05:32:13', '2024-10-22 05:32:13'),
(21, 'Nhiệm Vụ Triệu Đô - Cùng Con Xây Dựng Sự Nghiệp Thành Công', 45000, 90000, 'book72.webp', 15, '50%', 'Kinh Tế', '2024-10-22 05:34:14', '2024-10-22 05:34:14'),
(22, 'Combo Complete IELTS B2: Student\'s Book + Workbook (with answer & Audio CD)', 359691, 389000, 'book73.webp', 23, '8%', 'Tiếng Anh', '2024-10-22 05:38:38', '2024-10-22 05:38:38'),
(23, 'English Time 5 Student Book and Audio CD 2Ed', 180500, 190000, 'book75.webp', 13, '5%', 'Tiếng Anh', '2024-10-22 05:38:38', '2024-10-22 05:38:38'),
(24, 'Ứng Dụng Tâm Lý Học Thực Hành - Tâm Lý Học Trẻ Em', 97200, 99000, 'book76.webp', 43, '20%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-22 05:40:40', '2024-10-22 05:40:40'),
(25, 'Tâm Lý Học Toàn Thư - Tâm Lý Học Phát Triển', 138600, 165, 'book77.webp', 12, '16%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-22 05:41:17', '2024-10-22 05:41:17'),
(26, 'Ứng Dụng Tâm Lý Học Thực Hành - Tâm Lý Học Trẻ Em', 97200, 99000, 'book78.webp', 17, '20%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-22 05:45:10', '2024-10-22 05:45:10'),
(27, 'Hợp Tuyển Văn Học Văn Học Cổ Điển Hàn Quốc', 155800, 190000, 'book79.webp', 11, '18%', 'Văn Học', '2024-10-22 05:46:57', '2024-10-22 05:46:57'),
(28, 'Diễn Thuyết Việt Nam Nửa Đầu Thế Kỉ XX - Những Thanh Âm Văn Hóa Của Hội Trí Tri', 327000, 180000, 'book80.webp', 17, '18%', 'Văn Học', '2024-10-22 05:48:38', '2024-10-22 05:48:38'),
(29, '65 Truyện Ngắn Hay Dành Cho Thiếu Nhi', 478500, 550000, 'book82.webp', 17, '13%', 'Thiếu Nhi', '2024-10-22 05:52:21', '2024-10-22 05:52:21'),
(30, 'Truyện Thiếu Nhi Hạt Giống Tâm Hồn - Điều Ước Gửi Các Vì Sao', 44800, 56000, 'book83.webp', 12, '20%', 'Thiếu Nhi', '2024-10-22 05:52:21', '2024-10-22 05:52:21'),
(31, 'Bé Tô Màu Sáng Tạo - Nối Số Tô Màu 1', 26240, 32000, 'book84.webp', 23, '18%', 'Tô Màu', '2024-10-22 05:55:19', '2024-10-22 05:55:19'),
(32, 'Kinh Tế Học Cấm Đoán', 76000, 95000, 'book85.webp', 17, '20', '', '2024-10-22 05:56:31', '2024-10-22 05:56:31'),
(33, 'Tư Duy Kinh Tế Để Sống Tinh Tế - Tặng Kèm Bookmark', 144000, 180000, 'book87.webp', 43, '20%', 'Tâm Lý - Kỹ Năng Sống', '2024-10-22 06:03:02', '2024-10-22 06:03:02'),
(34, 'Văn Học Tuổi Hoa - Nhặt', 26950, 35000, 'book88.webp', 15, '23%', 'Thiếu Nhi', '2024-10-22 06:04:49', '2024-10-22 06:04:49');

-- --------------------------------------------------------

--
-- Table structure for table `cartmusic`
--

CREATE TABLE `cartmusic` (
  `id` int(11) NOT NULL,
  `image_cart` char(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `price` double NOT NULL,
  `priceold` double NOT NULL,
  `username` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cartmusic`
--

INSERT INTO `cartmusic` (`id`, `image_cart`, `title`, `price`, `priceold`, `username`, `created_at`, `updated_at`) VALUES
(48, 'book4.webp', 'Combo 6 sách kinh tế', 664200, 800000, 'hong', '2024-10-21 17:00:00', '2024-10-22 02:58:18'),
(62, 'book7.webp', 'Nói Chuyện Là Bản Năng, Giữ Miệng Là Tu Dưỡng, Im Lặng Là Trí Tuệ (Tái Bản)', 94500, 189000, 'hongne', '2024-10-25 17:00:00', '2024-10-26 13:56:07'),
(70, 'book8.webp', 'Giải Thích Ngữ Pháp Tiếng Anh (Tái Bản 2024)', 160200, 220000, 'HuyenMai', '2024-10-25 17:00:00', '2024-10-26 15:28:34'),
(74, 'book4.webp', 'Combo 6 sách kinh tế', 664200, 800000, 'HuyenMai', '2024-10-25 17:00:00', '2024-10-26 16:48:20'),
(84, 'book8.webp', 'Giải Thích Ngữ Pháp Tiếng Anh (Tái Bản 2024)', 160200, 220000, 'trunghieu', '2024-10-27 17:00:00', '2024-10-28 04:20:23'),
(85, 'book4.webp', 'Combo 6 sách kinh tế', 664200, 800000, 'trunghieu', '2024-10-27 17:00:00', '2024-10-28 15:53:12');

-- --------------------------------------------------------

--
-- Table structure for table `contact`
--

CREATE TABLE `contact` (
  `id` int(11) NOT NULL,
  `title` varchar(300) NOT NULL,
  `descript` varchar(2000) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `username` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact`
--

INSERT INTO `contact` (`id`, `title`, `descript`, `created_at`, `updated_at`, `username`) VALUES
(1, 'Nguyễn Trung Hiếu', 'và trần thị hồng', '2024-10-24 17:00:00', '2024-10-25 03:37:53', 'trunghieu'),
(5, 'ehehhe', 'hihihiih', '2024-10-24 17:00:00', '2024-10-25 03:44:06', 'trunghieu');

-- --------------------------------------------------------

--
-- Table structure for table `customer`
--

CREATE TABLE `customer` (
  `idcus` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `city` varchar(50) NOT NULL,
  `distric` varchar(50) NOT NULL,
  `ward` varchar(50) NOT NULL,
  `address` varchar(150) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `username` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customer`
--

INSERT INTO `customer` (`idcus`, `fullname`, `phone`, `city`, `distric`, `ward`, `address`, `created_at`, `updated_at`, `username`) VALUES
(7, 'Trung PiLO', '03898546', 'Tỉnh Bình Dương', 'Huyện Dầu Tiếng', 'Xã Thanh An', '112 đường lê trọng tấn', '2024-10-21 17:00:00', '2024-10-22 15:40:40', 'trunghieu'),
(12, 'Mai Huyền', '03895854', 'Tỉnh Bạc Liêu', 'Huyện Hoà Bình', 'Xã Minh Diệu', 'bố Quỳnh', '2024-10-25 17:00:00', '2024-10-26 15:26:33', 'HuyenMai'),
(14, 'Trung Kiên', '03895874', 'Tỉnh Bình Định', 'Huyện Tây Sơn', 'Xã Tây Giang', '223', '2024-10-27 17:00:00', '2024-10-28 04:08:31', 'trunghieu');

-- --------------------------------------------------------

--
-- Table structure for table `detailmusic`
--

CREATE TABLE `detailmusic` (
  `detailid` int(11) NOT NULL,
  `imagedt` char(100) NOT NULL,
  `supplier` varchar(200) NOT NULL,
  `nhaxuatban` varchar(100) NOT NULL,
  `author` varchar(100) NOT NULL,
  `hinhthuc` varchar(70) NOT NULL,
  `namxb` int(10) NOT NULL,
  `ngonngu` varchar(40) NOT NULL,
  `trongluong` int(10) NOT NULL,
  `size` varchar(50) NOT NULL,
  `sotrang` int(11) NOT NULL,
  `mota` varchar(2000) NOT NULL,
  `id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `detailmusic`
--

INSERT INTO `detailmusic` (`detailid`, `imagedt`, `supplier`, `nhaxuatban`, `author`, `hinhthuc`, `namxb`, `ngonngu`, `trongluong`, `size`, `sotrang`, `mota`, `id`, `created_at`, `updated_at`) VALUES
(1, 'book21.webp', 'MCbooks', 'Hồng Đức', 'Malcome Mann, Steve Taylore-', 'Bìa Mềm', 2023, 'Tiếng Anh', 350, '29 x 21 x 1 cm', 248, 'Bộ sách cung cấp từ vựng và ngữ pháp tiếng Anh cần thiết nhất dành cho người học đang có ý định thi các kỳ thi ở Level B1, B2, C1, C2 theo Khung tham chiếu châu Âu và mong muốn cải thiện năng lực tiếng Anh của bản thân.\r\nTrong mỗi cuốn sách sẽ bao gồm:\r\n- Từ vựng, ngữ pháp theo từng trình độ\r\n- Các bài review và bài test theo lộ trình\r\n- Hệ thống bài tập đa dạng, luyện tập sâu\r\n- Các bài tự kiểm tra, đánh giá mức độ hiểu kiến thức', 1, '2024-10-17 02:03:58', '2024-10-17 02:03:58'),
(2, 'book31.webp', '	\r\nNXB Trẻ', 'NXB Trẻ', '	\r\nThomas L Friedman, Jim Collins', 'Bìa Mềm', 2018, 'Tiếng Việt', 1250, '23 x 15.5 cm', 530, 'Jim Collins cùng nhóm nghiên cứu đã miệt mài nghiên cứu những công ty có bước nhảy vọt và bền vững để rút ra những kết luận sát sườn, những yếu tố cần kíp để đưa công ty từ tốt đến vĩ đại. Đó là những yếu tố khả năng lãnh đạo, con người, văn hóa, kỷ luật, công nghệ… Những yếu tố này được nhóm nghiên cứu xem xét tỉ mỉ và kiểm chứng cụ thể qua 11 công ty nhảy vọt lên vĩ đại. Mỗi kết luận của nhóm nghiên cứu đều hữu ích, vượt thời gian, ý nghĩa vô cùng to lớn đối với mọi lãnh đạo và quản lý ở mọi loại hình công ty (từ công ty có nền tảng và xuất phát tốt đến những công ty mới khởi nghiệp), và mọi lĩnh vực ngành nghề. Đây là cuốn sách nên đọc đối với bất kỳ lãnh đạo hay quản lý nào!Đây là bản dịch từ bản sách gốc mới nhất được sửa chữa, cập nhật và bổ sung hai chương mới nhất bởi chính tác giả. Xin trân trọng giới thiệu cùng bạn đọc.', 3, '2024-10-17 08:21:11', '2024-10-17 08:21:11'),
(3, 'book41.webp', 'Bách Việt', 'Lao Động', '	\r\nSong Hong Bing', '	\r\nBìa Mềm', 2022, 'Tiếng Việt', 3385, '24 x 16 x 1.5 cm', 1406, '1. Chiến Tranh Tiền Tệ - Phần 1 - Ai Thực Sự Là Người Giàu Nhất Thế Giới?1. Chiến Tranh Tiền Tệ - Phần 1 - Ai Thực Sự Là Người Giàu Nhất Thế Giới?\r\n2. Chiến Tranh Tiền Tệ - Phần 2 - Sự Thống Trị Của Quyền Lực Tài Chính (Tái Bản 2022)\r\n3. CHIẾN TRANH TIỀN TỆ - PHẦN III \r\n4. Chiến Tranh Tiền Tệ Phần IV: Siêu Cường Về Tài Chính - Tham Vọng Về Đồng Tiền Chung Châu Á\r\n5. Chiến tranh tiền tệ tập 5\r\nCuốn sách tập trung vào phân tích hiện trạng của nền kinh tế Mỹ và đồng đô la thông qua thị trường vàng, thị trường chứng khoán, cũng như tìm hiểu dòng vốn thông qua thị trường trái phiếu, khám phá lĩnh vực tài chính thông qua thị trường mua lại, tìm hiểu căn nguyên các cuộc khủng hoảng thông qua lãi suất, bất dộng sản, thị trường việc làm…\r\n\r\nNgoài ra, cuốn sách còn mở rộng tầm quan sát của độc giả đến giai đoạn những năm 2000, từ quan sát cận cảnh nền kinh tế Mỹ cho đến giai đoạn đánh giá và nắm bắt ngọn nguồn trong lịch sử, từ đó phục dựng lại một cách chân thực nhất mô hình của quá trình suy vong, từ đó hiểu được bản chất của tiền tệ cùng những định hướng, hướng đi trong tương lai.', 4, '2024-10-17 08:28:50', '2024-10-17 08:28:50'),
(4, 'book51.webp', 'Nhà Xuất Bản Kim Đồng', 'NXB Kim Đồng', 'Đoàn Giỏi', 'Bìa Mềm', 2020, 'Tiếng Việt', 320, '14.5 x 20.5 cm', 304, 'Cuộc đời lưu lạc của chú bé An qua những miền đất rừng phương Nam thời kì đầu cuộc kháng chiến chống Pháp. Một vùng đất trù phú, đa dạng, kì vĩ với những kênh rạch, tôm cá, chim chóc, muông thú, lúa gạo... và cây cối, rừng già. Trong thế giới đó có những con người vô cùng nhân hậu như cha mẹ nuôi của bé An, như cậu bé Cò, chú Võ Tòng... cùng những người anh em giàu lòng yêu quê hương, đất nước. Cuộc sống tự do và cuộc đời phóng khoáng cởi mở đã để lại ấn tượng sâu sắc trong tâm khảm người đọc nhiều thế hệ suốt những năm tháng qua', 5, '2024-10-17 08:32:48', '2024-10-17 08:32:48'),
(5, 'book61.webp', 'Nhà Xuất Bản Kim Đồng', 'Nhà Xuất Bản Kim Đồng', 'Nguyễn Huy Tưởng', 'Bìa Mềm', 2024, 'Tiếng Việt', 585, '22.5 x 14 x 2.5 cm', 500, 'Sống mãi với Thủ đô là thiên sử thi hào hùng bi tráng về cuộc chiến đấu bảo vệ Thủ đô sau ngày toàn quốc kháng chiến (năm 1946) của những con người quả cảm. Sống mãi với Thủ đô đã xây dựng bức tranh sống động về Hà Nội và tình yêu tha thiết tự hào về người Hà Nội hào hoa, thanh lịch mà bất khuất kiên cường…\r\n\r\n“Tôi tin rằng chúng ta, những người đã trải qua cuộc kháng chiến chín năm và lớp người lớn lên trong hòa bình sau này sẽ cảm ơn nhà văn ở nhiều trang tuyệt đẹp về những ngày Thủ đô chuẩn bị kháng chiến, và hai đêm đầu tiên của cuộc chiến đấu đầy ác liệt ấy. Anh [Nguyễn Huy Tưởng] đã ghi chép một cách tỉ mỉ và tài tình cái không khí bị nén chặt đang đợi cơ hội bùng nổ ra, những cảm giác hết sức tinh tế trong giờ phút nghiêm trang nhất của lịch sử… Cảnh nọ và cảnh kia chen lẫn nhau, như những tia sáng hướng dẫn người đọc lần lần tìm ra toàn cảnh, một bức tranh rất nhiều màu sắc.” - NGUYỄN KHẢI\r\n\r\n“Tôi đọc Sống mãi với Thủ đô đã mấy chục năm nay thế mà vẫn còn giữ nguyên vẹn trong trí nhớ một chiếc lá sấu vàng khô cong như một tấm vàng giát, từ từ và lặng lẽ gieo mình xuống vạt cỏ ven Hồ Gươm trong một buổi chiều mùa đông của năm ấy – mùa đông 1946 – trong một sắc trời một màu xám đầy lạnh lẽo chứa đựng một cái gì gai gai, rờn rợn mà tôi có cảm tưởng chỉ ngòi bút của Nguyễn Huy Tưởng mới tả được hay đến như thế – về mấy ngày trước khi cuộc kháng chiến trường kì nổ ra.” - NGUYỄN MINH CHÂU', 9, '2024-10-17 08:36:44', '2024-10-17 08:36:44'),
(6, 'book71.webp', '1980 books', 'Thanh Niên', 'Trương Tiếu Hằng', 'Bìa Mềm', 2022, 'Tiếng Việt', 350, '20.5 x 13 x 2 cm', 403, 'Tuân Tử nói: “Nói năng hợp lý, đó gọi là hiểu biết; im lặng đúng lúc, đó cũng là hiểu biết”. Ngôn ngữ là thứ có thể thể hiện rõ nhất mức độ tu dưỡng của một người, nói năng hợp lý là một loại trí tuệ, mà im lặng đúng lúc cũng là một loại trí tuệ. Nếu một người không biết giữ miệng, nói mà không suy nghĩ, nghĩ gì nói nấy, tất nhiên rất dễ khiến người khác chán ghét.\r\n\r\nNội dung quyển sách này xoay quanh hai vấn đề đó là “biết cách nói chuyện” và “biết giữ miệng”, thông qua 12 chương sách nói rõ cách nói chuyện với những người khác nhau, cách nói chuyện trong những trường hợp khác nhau, làm thế nào để nắm vững những kỹ năng và chừng mực để nói chuyện cho khôn khéo, những người không giỏi ăn nói làm cách nào mới có thể nói được những lời thích hợp với đúng người và đúng thời điểm, để có thể ứng phó với những trường hợp khác nhau trong giao tiếp.\r\n\r\nNgười biết nói chuyện, ẩn sau con người họ là lòng tốt đã khắc sâu vào xương tủy, là sự tôn trọng đến từ việc đặt mình vào vị trí của người khác, là trí tuệ sâu sắc, độc đáo và lòng kiên nhẫn không ngại phiền hà. Họ chưa hẳn là những người giỏi ăn nói, nhưng mỗi khi nói đều làm người khác như được tắm trong gió xuân, vừa mở miệng là đã toát lên khí chất hơn người. ', 10, '2024-10-17 08:40:04', '2024-10-17 08:40:04'),
(7, 'book81.webp', 'Zenbooks', 'Đà Nẵng', 'Mai Lan Hương, Hà Thanh Uyên', 'Bìa Mềm', 2021, 'Tiếng Việt', 560, '24 x 17 x 2.5 cm', 560, 'Ngữ pháp Tiếng Anh tổng hợp các chủ điểm ngữ pháp trọng yếu mà học sinh cần nắm vững. Các chủ điểm ngữ pháp được trình bày rõ ràng, chi tiết. Sau mỗi chủ điểm ngữ pháp là phần bài tập & đáp án nhằm giúp các em củng cố kiến thức đã học, đồng thời tự kiểm tra kết quả.\r\n\r\nSách Giải Thích Ngữ Pháp Tiếng Anh, tác Mai Lan Hương – Hà Thanh Uyên, là cuốn sách ngữ pháp đã được phát hành và tái bản rất nhiều lần trong những năm qua.\r\n\r\nGiải Thích Ngữ Pháp Tiếng Anh được biên soạn thành 9 chương, đề cập đến những vấn đề ngữ pháp từ cơ bản đến nâng cao, phù hợp với mọi trình độ. Các chủ điểm ngữ pháp trong từng chương được biên soạn chi tiết, giải thích cặn kẽ các cách dùng và quy luật mà người học cần nắm vững. Sau mỗi chủ điểm ngữ pháp là phần bài tập đa dạng nhằm giúp người học củng cố lý thuyết.\r\n\r\nHy vọng Giải Thích Ngữ Pháp Tiếng Anh sẽ là một quyển sách thiết thực, đáp ứng yêu cầu học, ôn tập và nâng cao trình độ ngữ pháp cho người học và là quyển sách tham khảo bổ ích dành cho giáo viên.', 11, '2024-10-17 08:43:32', '2024-10-17 08:43:32'),
(8, 'book91.webp\r\n', 'CÔNG TY CỔ PHẦN VĂN HOÁ & CÔNG NGHỆ TUỆ TRI', 'Dân Trí', 'M. Scott Peck', 'Bìa Mềm', 2024, 'Tiếng Việt', 360, '21 x 14.5 x 1.7 cm', 344, 'Có lẽ không quyển sách nào trong thế kỷ này có tác động sâu sắc đến đời sống trí tuệ và tinh thần của chúng ta hơn Con Đường Chẳng Mấy Ai Đi. Với doanh số trên 10 triệu bản in trên toàn thế giới và được dịch sang hơn 25 ngôn ngữ, đây là một hiện tượng trong ngành xuất bản, với hơn mười năm nằm trong danh sách Best-sellers của New York Times.\r\n\r\nVới cách hành văn kinh điển và thông điệp đầy thấu hiểu, quyển sách Con Đường Chẳng Mấy Ai Đi giúp chúng ta khám phá bản chất của các mối quan hệ và của một tinh thần trưởng thành. Quyển sách giúp chúng ta học cách phân biệt sự lệ thuộc với tình yêu; làm thế nào để trở thành những bậc phụ huynh tinh tế và nhạy cảm; và cuối cùng là làm thế nào để sống chân thật với chính mình.\r\n\r\nVới dòng mở đầu bất hủ của quyển sách, \"Cuộc đời này rất khó sống\", thể hiện quan điểm hành trình phát triển tinh thần là một chặng đường dài và gian nan, Tiến sĩ Peck thể hiện sự đồng cảm, nhẹ nhàng dẫn dắt độc giả vượt qua quá trình khó khăn đó, để thay đổi hướng tới tầm mức thấu hiểu bản thân sâu sắc hơn.', 12, '2024-10-17 08:46:33', '2024-10-17 08:46:33'),
(9, 'book101.webp', 'Huy Hoang bookstore', 'NXB Thanh Niên', 'Yoshichi Shimada', 'Bìa Mềm', 2021, 'Tiếng Việt', 100, '18.5 x 13 cm', 216, 'Hạnh phúc không phải là thứ được định đoạt bằng tiền. Hạnh phúc phải được định đoạt bằng tâm thế của mỗi chúng ta.', 13, '2024-10-17 09:42:05', '2024-10-17 09:42:05'),
(10, 'book161.webp', 'Ngô Xuân Hội', 'Hồng Đức', 'Ngô Xuân Hội', 'Bìa Cứng', 2024, 'Tiếng Việt', 585, '20.5 x 14.5 x 3 cm', 522, 'Nhiều người bảo, văn là người. Mình cũng nghĩ, văn là người. Như mình, tính chân chân chân, thật thật thật nên thơ văn viết ra cũng chân chân chân, thật thật thật. Lắm lúc chán, nghĩ cứ theo mãi lá cờ nôm thế này khá lắm cũng khó mà bằng được các bác Trần Hữu Thung Võ Văn Trực. Tính thay đổi bứt phá cho văn chương vượt lên: Đồ sộ như Tô Hoài, sắc sảo thông minh như Chế Lan Viên, đáo để như Nguyễn Huy Thiệp, đắm đuối như Xuân Quỳnh, hào hoa như Lưu Quang Vũ, cuồn cuộn như Thanh Thảo, ám ảnh như Hữu Thỉnh, ngoa ngoắt như Trần Mạnh Hảo, lão thực như Trần Nhuận Minh, nhạy cảm như Nguyễn Trọng Tạo... Nhưng “Non sông dễ đổi, bản tính khó dời” nên đâu lại hoàn đấy.\r\n\r\nTrần Chấn Uy cho không phải vậy. Theo hắn quan hệ giữa nhà văn và tác phẩm là một phân số, trong đó tác phẩm là tử số, tác giả là mẫu số. Phân số này không bao giờ lớn hơn 1. Hắn giải thích, tử số là tác phẩm là một phần thế giới tưởng tượng của người viết, còn mẫu số gồm toàn bộ thế giới tưởng tượng cộng với cái xác phàm của nhà văn:“Thế giới thực ông biết rồi, hữu hạn thôi, trong khi đó thế giới tưởng tượng là vô hạn. Độ lớn của nhà văn được đo bằng sự phát lộ của cái vô hạn này, đấy là tác phẩm, là phần nổi của tảng băng trôi...”. Nói đến đây, hắn lấy một số người ra làm ví dụ. Đó là những người tiếp xúc ở ngoài đời rất thú vị, tính hóm hỉnh, nhìn đâu cũng thấy sự tức cười mà thơ văn viết ra nghiêm chỉnh đến khó chịu...', 14, '2024-10-22 06:11:20', '2024-10-22 06:11:20'),
(11, 'book90.webp', 'Skybooks', 'NXB Phụ Nữ Việt Nam', 'Kulzsc', 'Bìa Mềm', 2022, 'Tiếng Anh', 150, '24 x 19 x 0.4 cm', 96, 'Sau thành công của cuốn sách đầu tay “Phải lòng với cô đơn” chàng họa sĩ nổi tiếng và tài năng Kulzsc đã trở lại với một cuốn sách vô cùng đặc biệt mang tên: \"Tô bình yên - vẽ hạnh phúc” – sắc nét phong cách cá nhân với một chút “thơ thẩn, rất hiền”.\r\n\r\nKhông giống với những cuốn sách chỉ để đọc, “Tô bình yên – vẽ hạnh phúc” là một cuốn sách mà độc giả vừa tìm được “Hạnh phúc to to từ những điều nho nhỏ” vừa được thực hành ngay lập tức. Một sự kết hợp mới lạ đầy thú vị giữa thể loại sách tản văn và sách tô màu. Lật mở cuốn sách này, bạn sẽ thấy vô vàn những điều nhỏ bé dễ thương lan tòa nguồn năng lượng tích cực. Và kèm theo một list những điều tuyệt vời khiến bạn không thể bỏ lỡ:\r\n\r\n- Tận tình chỉ dẫn: 8 hướng dẫn tô màu đầy hứng thú từ Kulzsc\r\n\r\n- Tranh vẽ đơn giản, gần gũi. Nét vẽ đáng yêu, dễ thương\r\n\r\n- Chủ đề xoay quanh những điều bình yên trong cuộc sống: Đọc sách, đi dạo, dọn dẹp nhà cửa, trồng cây, ăn cơm mẹ nấu, nghe một bản nhạc hay, và nghĩ về nụ cười của một ai đó…\r\n\r\n- Mỗi bức tranh lại là những lời thủ thỉ, tâm tình của tác giả. Là những điều nhắn gửi nho nhỏ mong bạn bớt đi những xao động:\r\n\r\n“Em chọn hạnh phúc\r\n\r\nEm chọn bình yên\r\n\r\nEm chọn bên cạnh\r\n\r\nNhững điều an yên”', 15, '2024-10-22 06:20:05', '2024-10-22 06:20:05'),
(12, 'book92.webp', 'AZ Việt Nam', 'Dân Trí', 'Ying Shu, Monhiart', 'Bìa Mềm', 2024, 'Tiếng Việt', 120, '24 x 19 x 0.5 cm', 104, 'Nếu nói “Cùng Bạn Trưởng Thành” đơn thuần chỉ là cuốn sách đã từng ghi dấu sâu đậm trong lòng các độc giả với phiên bản song ngữ Trung – Việt đầy ý nghĩa, như một bước nhảy rút ngắn quãng đường chinh phục tiếng Trung cho các sĩ tử, thì giờ đây, phiên bản Tô màu cuộc sống của “Cùng Bạn Trưởng Thành” sẽ là một tuyển tập ghi lại những trải nghiệm mới mẻ và thú vị trong cuộc sống của bạn dưới hình thức những gam màu tươi trẻ, tinh khôi.\r\n\r\nVẫn là những trích dẫn quen thuộc từng khiến bạn lưu luyến không rời ngày ấy, giờ đây được tô điểm thêm những nét vẽ trẻ trung, sống động để bạn có thể mở rộng tâm hồn của mình hơn, đón nhận những điều tươi đẹp hơn, không chỉ khám phá trên phương diện ngôn từ mà còn dẫn lối cho sự sáng tạo đạt đến thăng hoa, khơi gợi cảm hứng sống để bạn bước tiếp trên hành trình trưởng thành.\r\n\r\n“Mong bạn sẽ giống như những đóa hoa hướng dương, hướng về phía mặt trời, mỗi ngày đều hấp thụ những năng lượng tích cực, vươn mình bung tỏa rực rỡ.”\r\n\r\n“Cùng Bạn Trưởng Thành”– Phiên bản Tô màu cuộc sống hứa hẹn sẽ mang đến cho bạn những bất ngờ tươi trẻ và tràn đầy sức sống, giúp bạn khám phá bản thân, luôn bên bạn qua năm dài tháng rộng và cùng bạn trưởng thành!', 16, '2024-10-22 06:23:41', '2024-10-22 06:23:41'),
(13, 'book93.webp', 'Zenbooks', 'Thế Giới', 'Bino Chém Tiếng Anh', 'Bìa Mềm', 2024, 'Tiếng Việt', 400, '20.5 x 14 x 1 cm', 200, '“Phần lớn người Việt đều biết tiếng Anh NHIỀU HƠN HỌ NGHĨ, chỉ là họ chưa biết làm thế nào để đưa ý tưởng thành lời nói mà thôi!” - Bino chém tiếng Anh\r\n\r\nBiết nhiều tiếng Anh nhưng không… nói được - điều này có đúng với bạn không? Sao 12 năm học tiếng Anh không giúp chúng ta xử lý được những cuộc trò chuyện thông thường?\r\n\r\nSao điểm tiếng Anh trên lớp toàn 9, 10 nhưng gặp Tây lại ấp a ấp úng? Sao sở hữu điểm IELTS 7.0+ nhưng vẫn “sốc ngôn ngữ” khi ra nước ngoài?\r\n\r\nNguyên nhân có lẽ nằm nhiều ở cách giáo dục truyền thống tại Việt Nam - vốn nặng tính học thuật, thiếu “hơi thở đời thường”, ít luyện tập và ít nhấn mạnh vào thứ ngôn ngữ tự nhiên mà người bản xứ thường nói với nhau.\r\n\r\n“Chém tiếng Anh không cần động não” của tác giả Bino - chủ kênh TikTok @binochemtienganh với hơn 750 nghìn người theo dõi - được thiết kế để giải quyết điểm khó cố hữu trên. Với cách tiếp cận nặng tính thực tiễn, thực chiến và luyện tập - đây chính là cuốn sách luyện nói tiếng Anh dành cho những ai đang muốn thực sự nói được tiếng Anh trong đời sống.', 17, '2024-10-22 06:28:46', '2024-10-22 06:28:46');

-- --------------------------------------------------------

--
-- Table structure for table `notifycation`
--

CREATE TABLE `notifycation` (
  `id` int(11) NOT NULL,
  `title` varchar(300) NOT NULL,
  `daynow` timestamp NOT NULL DEFAULT current_timestamp(),
  `descript` varchar(1000) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `username` varchar(50) NOT NULL,
  `detailid` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifycation`
--

INSERT INTO `notifycation` (`id`, `title`, `daynow`, `descript`, `created_at`, `updated_at`, `username`, `detailid`) VALUES
(3, 'Nguyễn Trung Hiếu', '2024-10-23 16:22:27', 'Code đi thằng ngu', '2024-10-23 16:22:27', '2024-10-23 16:22:27', 'trunghieu', 2),
(7, 'Trần thị Hồng', '2024-10-23 16:29:03', 'Hồng Mị ', '2024-10-23 16:29:03', '2024-10-23 16:29:03', 'trunghieu', 1);

-- --------------------------------------------------------

--
-- Table structure for table `oderstatus`
--

CREATE TABLE `oderstatus` (
  `status` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `oderstatus`
--

INSERT INTO `oderstatus` (`status`) VALUES
('Đã đặt'),
('Đã giao'),
('Đã hủy'),
('Đang vận chuyển'),
('Trả hàng');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `username` varchar(50) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(50) NOT NULL,
  `pass` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`username`, `fullname`, `phone`, `email`, `pass`, `created_at`, `updated_at`) VALUES
('hong', 'Trần Thị Hồng Mị', '03465867', '<EMAIL>', '12345678H', '2024-10-21 17:00:00', '2024-10-22 02:56:33'),
('hongne', 'Trần Thị Hồng', '089289455', '<EMAIL>', '11111111', '2024-10-25 17:00:00', '2024-10-26 13:55:27'),
('HuyenMai', 'Mai Thu Huyền', '090958435', '<EMAIL>', '11111111', '2024-10-25 17:00:00', '2024-10-26 14:03:27'),
('trunghieu', 'Nguyễn Trung Hiếu', '09374856', '<EMAIL>', '22222222H', '2024-10-03 17:00:00', '2024-10-04 12:37:50');

-- --------------------------------------------------------

--
-- Table structure for table `voucher`
--

CREATE TABLE `voucher` (
  `idvou` int(11) NOT NULL,
  `saleof` double NOT NULL,
  `limit` double NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `username` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `voucher`
--

INSERT INTO `voucher` (`idvou`, `saleof`, `limit`, `created_at`, `updated_at`, `username`) VALUES
(1, 25000, 100000, '2024-10-22 17:24:28', '2024-10-22 17:24:28', 'trunghieu'),
(2, 10000, 50000, '2024-10-22 17:24:28', '2024-10-22 17:24:28', 'trunghieu'),
(3, 50000, 200000, '2024-10-26 13:56:47', '2024-10-26 13:56:47', 'hongne'),
(4, 50000, 300000, '2024-10-26 14:16:15', '2024-10-26 14:16:15', 'HuyenMai'),
(5, 55000, 200000, '2024-10-28 04:20:02', '2024-10-28 04:20:02', 'trunghieu');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `bill`
--
ALTER TABLE `bill`
  ADD PRIMARY KEY (`idbill`),
  ADD KEY `fkStatus` (`status`),
  ADD KEY `fkcus` (`idcus`),
  ADD KEY `fkusername` (`username`);

--
-- Indexes for table `books`
--
ALTER TABLE `books`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cartmusic`
--
ALTER TABLE `cartmusic`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_user` (`username`);

--
-- Indexes for table `contact`
--
ALTER TABLE `contact`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fkContact` (`username`);

--
-- Indexes for table `customer`
--
ALTER TABLE `customer`
  ADD PRIMARY KEY (`idcus`),
  ADD KEY `fk_username` (`username`);

--
-- Indexes for table `detailmusic`
--
ALTER TABLE `detailmusic`
  ADD PRIMARY KEY (`detailid`),
  ADD KEY `id_detail` (`id`);

--
-- Indexes for table `notifycation`
--
ALTER TABLE `notifycation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_notify` (`username`),
  ADD KEY `fk_detail` (`detailid`);

--
-- Indexes for table `oderstatus`
--
ALTER TABLE `oderstatus`
  ADD PRIMARY KEY (`status`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `voucher`
--
ALTER TABLE `voucher`
  ADD PRIMARY KEY (`idvou`),
  ADD KEY `fk_vouname` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `bill`
--
ALTER TABLE `bill`
  MODIFY `idbill` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=110;

--
-- AUTO_INCREMENT for table `books`
--
ALTER TABLE `books`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT for table `cartmusic`
--
ALTER TABLE `cartmusic`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=86;

--
-- AUTO_INCREMENT for table `contact`
--
ALTER TABLE `contact`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `customer`
--
ALTER TABLE `customer`
  MODIFY `idcus` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `detailmusic`
--
ALTER TABLE `detailmusic`
  MODIFY `detailid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `notifycation`
--
ALTER TABLE `notifycation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `voucher`
--
ALTER TABLE `voucher`
  MODIFY `idvou` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bill`
--
ALTER TABLE `bill`
  ADD CONSTRAINT `fkStatus` FOREIGN KEY (`status`) REFERENCES `oderstatus` (`status`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fkcus` FOREIGN KEY (`idcus`) REFERENCES `customer` (`idcus`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `cartmusic`
--
ALTER TABLE `cartmusic`
  ADD CONSTRAINT `fk_user` FOREIGN KEY (`username`) REFERENCES `user` (`username`);

--
-- Constraints for table `contact`
--
ALTER TABLE `contact`
  ADD CONSTRAINT `fkContact` FOREIGN KEY (`username`) REFERENCES `user` (`username`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `customer`
--
ALTER TABLE `customer`
  ADD CONSTRAINT `fk_username` FOREIGN KEY (`username`) REFERENCES `user` (`username`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Constraints for table `detailmusic`
--
ALTER TABLE `detailmusic`
  ADD CONSTRAINT `id_detail` FOREIGN KEY (`id`) REFERENCES `books` (`id`);

--
-- Constraints for table `notifycation`
--
ALTER TABLE `notifycation`
  ADD CONSTRAINT `fk_detail` FOREIGN KEY (`detailid`) REFERENCES `detailmusic` (`detailid`),
  ADD CONSTRAINT `fk_notify` FOREIGN KEY (`username`) REFERENCES `user` (`username`);

--
-- Constraints for table `voucher`
--
ALTER TABLE `voucher`
  ADD CONSTRAINT `fk_vouname` FOREIGN KEY (`username`) REFERENCES `user` (`username`) ON DELETE NO ACTION ON UPDATE NO ACTION;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
