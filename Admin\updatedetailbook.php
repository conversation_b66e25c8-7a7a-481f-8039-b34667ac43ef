<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 
header("Access-Control-Allow-Methods: PUT, POST, GET, OPTIONS"); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Kiểm tra ID chi tiết sách
    $detailid = $_POST['detailid'] ?? null;
    if (!$detailid) {
        echo json_encode(['status' => 0, 'message' => 'Không tìm thấy detailid.']);
        exit();
    }

    // Lấy các thông tin chi tiết sách
    $supplier = $_POST['supplier'] ?? null;
    $nhaxuatban = $_POST['nhaxuatban'] ?? null;
    $author = $_POST['author'] ?? null;
    $hinhthuc = $_POST['hinhthuc'] ?? null;
    $namxb = $_POST['namxb'] ?? null;
    $ngonngu = $_POST['ngonngu'] ?? null;
    $trongluong = $_POST['trongluong'] ?? null;
    $size = $_POST['size'] ?? null;
    $sotrang = $_POST['sotrang'] ?? null;
    $mota = $_POST['mota'] ?? null;
    $created_at = date('Y-m-d');
    $image_path = null;

    // Kiểm tra và xử lý ảnh
    if (!empty($_FILES['imagedt']['name'])) {
        $image = $_FILES['imagedt']['name'];
        $image_tmp = $_FILES['imagedt']['tmp_name'];
        $target_dir = $_SERVER['DOCUMENT_ROOT'] . "/bookapp/public/image/";
        $target_dir2 = $_SERVER['DOCUMENT_ROOT'] . "/admin/public/image/";
        $target_file = $target_dir . basename($image);
        $target_file2 = $target_dir2 . basename($image);

        // Di chuyển ảnh đến cả hai thư mục
        if (move_uploaded_file($image_tmp, $target_file)) {
            $image_path = basename($image);
            if (!copy($target_file, $target_file2)) {
                echo json_encode(['status' => 0, 'message' => 'Không thể sao chép ảnh đến thư mục thứ hai.']);
                exit();
            }
        } else {
            echo json_encode(['status' => 0, 'message' => 'Tải ảnh lên không thành công.']);
            exit();
        }
    }

    // Câu truy vấn SQL để cập nhật detailmusic
    $sql = "UPDATE detailmusic SET supplier = :supplier, nhaxuatban = :nhaxuatban, author = :author, 
            hinhthuc = :hinhthuc, namxb = :namxb, ngonngu = :ngonngu, trongluong = :trongluong, 
            `size` = :size, sotrang = :sotrang, mota = :mota, created_at = :created_at";
    if ($image_path) {
        $sql .= ", imagedt = :imagedt"; // Thêm trường imagedt nếu có ảnh
    }
    $sql .= " WHERE detailid = :detailid";

    $stmt = $conn->prepare($sql);

    // Gán giá trị
    $stmt->bindParam(':supplier', $supplier);
    $stmt->bindParam(':nhaxuatban', $nhaxuatban);
    $stmt->bindParam(':author', $author);
    if ($image_path) {
        $stmt->bindParam(':imagedt', $image_path); // Gán giá trị ảnh nếu có
    }
    $stmt->bindParam(':hinhthuc', $hinhthuc);
    $stmt->bindParam(':namxb', $namxb);
    $stmt->bindParam(':ngonngu', $ngonngu);
    $stmt->bindParam(':trongluong', $trongluong);
    $stmt->bindParam(':size', $size);
    $stmt->bindParam(':sotrang', $sotrang);
    $stmt->bindParam(':mota', $mota);
    $stmt->bindParam(':created_at', $created_at);
    $stmt->bindParam(':detailid', $detailid);

    // Thực thi và kiểm tra số bản ghi cập nhật
    if ($stmt->execute()) {
        if ($stmt->rowCount() > 0) {
            $response = ['status' => 1, 'message' => 'Cập nhật thông tin chi tiết sách thành công.'];
        } else {
            $response = ['status' => 0, 'message' => 'Không có thay đổi nào trong dữ liệu.'];
        }
    } else {
        $errorInfo = $stmt->errorInfo();
        $response = [
            'status' => 0, 
            'message' => 'Cập nhật thông tin chi tiết sách thất bại.',
            'error' => $errorInfo
        ];
    }

    echo json_encode($response);
}


?>
