<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); // <PERSON><PERSON><PERSON> bảo trả về JSON

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case "GET":
        if (isset($_GET['id'])) {
            $id = $_GET['id'];
            try {
          
                // Sử dụng prepared statement để tránh SQL injection
                $sql = "SELECT product.*, detailmusic.*
                        FROM product
                        INNER JOIN detailmusic ON product.id = detailmusic.id
                        WHERE product.id = :id";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':id', $id, PDO::PARAM_INT);
                $stmt->execute();
                $bookDetail = $stmt->fetch(PDO::FETCH_ASSOC);

                if (empty($bookDetail)) {
                    echo json_encode(['status' => 0, 'msg' => 'Không tìm thấy chi tiết  nhạc cụ.']);
                } else {
                    echo json_encode(['status' => 1, 'book' => $bookDetail]);
                }
            } catch (PDOException $e) {
                // Trả về lỗi dưới dạng JSON
                echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
            }
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số id.']);
        }
        break;
    
    default:
        echo json_encode(['status' => 0, 'msg' => 'Phương thức không được hỗ trợ.']);
        break;
}
?>
