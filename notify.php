<?php 
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $sql = "SELECT * FROM notifycation";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>
