<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8');

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case "GET":
        try {
            $status = $_GET['status'] ?? null;
            if ($status === null) {
                echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số status']);
                exit;
            }

            // Sử dụng LIKE để tìm kiếm gần đúng
            $sql = "SELECT  bill.* , customer.* FROM bill
            INNER JOIN customer ON bill.idcus = customer.idcus
            WHERE status = :status";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':status', $status, PDO::PARAM_STR);
            $stmt->execute();
            $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($listbook)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
            } else {
                echo json_encode(['status' => 1, 'books' => $listbook]);
            }
        } catch (PDOException $e) {
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
        break;
}
?>
