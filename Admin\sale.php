<?php
 error_reporting(E_ALL);
 ini_set("display_errors", 1);

 // Header cho phép truy cập từ mọi nguồn (CORS)
 header("Access-Control-Allow-Headers: Content-Type, Authorization");
 header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 
 header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $sql = "SELECT * FROM voucher";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có thông báo nào nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if($method == "DELETE") {
    try {
        $idvou = $_GET['idvou'];

        // Kiểm tra xem username có được cung cấp không
        if (empty($idvou)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM voucher WHERE idvou = :idvou";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':idvou', $idvou);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    // Decode JSON data
    $cartmusic = json_decode(file_get_contents('php://input'));

    // Check if required fields are present
    if (!isset($cartmusic->saleof, $cartmusic->limit, $cartmusic->username)) {
        echo json_encode(['status' => 0, 'message' => 'Thiếu thông tin bắt buộc.']);
        exit;
    }

    // Prepare SQL statement
    $sql = "INSERT INTO voucher (idvou, saleof, `limit`, username, created_at) 
            VALUES (null, :saleof, :limit, :username, :created_at)";
    $stmt = $conn->prepare($sql);

    // Set current date
    $created_at = date('Y-m-d');

    // Bind parameters
    $stmt->bindParam(':saleof', $cartmusic->saleof);
    $stmt->bindParam(':limit', $cartmusic->limit);
    $stmt->bindParam(':username', $cartmusic->username);
    $stmt->bindParam(':created_at', $created_at);

    // Execute and check for success
    if ($stmt->execute()) {
        $response = ['status' => 1, 'message' => 'Thêm mã thành công.'];
    } else {
        $response = ['status' => 0, 'message' => 'Thêm mã thất bại.'];
    }

    echo json_encode($response);
}


?>