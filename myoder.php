<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); // Đ<PERSON>m bảo trả về JSON

include 'DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];



switch ($method) {
    case "GET":
        try {
            // Kiểm tra xem idcus có trong URL không
            $idcus = $_GET['idcus'] ?? null; 
            if ($idcus === null) {
                echo json_encode(['status' => 0, 'msg' => 'Thiếu tham số idcus']);
                exit;
            }

            // Truy vấn để lấy tất cả dữ liệu từ `bill` và `cartmusic`
            $sql = "SELECT bill.* 
                    FROM bill 
                    WHERE bill.idcus = :idcus"; // Sửa dấu ngoặc kép

            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':idcus', $idcus, PDO::PARAM_INT); // Gán giá trị cho :idcus
            $stmt->execute();
            
            $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($listbook)) {
                echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
            } else {
                echo json_encode(['status' => 1, 'books' => $listbook]);
            }
        } catch (PDOException $e) {
            // Trả về lỗi dưới dạng JSON
            echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
        }
        break;
        case "POST": {
            try {
        
                $data = json_decode(file_get_contents("php://input"), true);
                $idbill = $data['idbill']; 
                $status = $data['status'];
                
        
                $sql = "UPDATE bill SET status = :status WHERE idbill = :idbill";
                $stmt = $conn->prepare($sql);
                
                $stmt->bindParam(':status', $status, PDO::PARAM_STR);      
                $stmt->bindParam(':idbill', $idbill, PDO::PARAM_STR);
                
                if ($stmt->execute()) {
                    echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
                } else {
                    echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
                }
            } catch (PDOException $e) {
                echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
            }
            break;
        }
}
?>
