<?php
 error_reporting(E_ALL);
 ini_set("display_errors", 1);

 // Header cho phép truy cập từ mọi nguồn (CORS)
 header("Access-Control-Allow-Headers: Content-Type, Authorization");
 header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 
 header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        // Query to fetch all books
        $sql = "SELECT  bill.* , customer.* FROM bill
        INNER JOIN customer ON bill.idcus = customer.idcus";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
        // Query to count the number of books with status 'Đã giao'
        $sqlDelivered = "SELECT COUNT(*) AS total_quality FROM bill  INNER JOIN customer ON bill.idcus = customer.idcus WHERE status = 'Đã giao'";
        $stmtDelivered = $conn->prepare($sqlDelivered);
        $stmtDelivered->execute();
        $deliveredCount = $stmtDelivered->fetch(PDO::FETCH_ASSOC)['total_quality'];
    
        // Query to count the number of books with status 'Đã đặt'
        $sqlOrdered = "SELECT COUNT(*) AS total_quality FROM bill  INNER JOIN customer ON bill.idcus = customer.idcus WHERE status = 'Đã đặt'";
        $stmtOrdered = $conn->prepare($sqlOrdered);
        $stmtOrdered->execute();
        $orderedCount = $stmtOrdered->fetch(PDO::FETCH_ASSOC)['total_quality'];
    
        // Query to count the number of books with status 'Đã hủy'
        $sqlCancelled = "SELECT COUNT(*) AS total_quality FROM bill  INNER JOIN customer ON bill.idcus = customer.idcus WHERE status = 'Đã hủy'";
        $stmtCancelled = $conn->prepare($sqlCancelled);
        $stmtCancelled->execute();
        $cancelledCount = $stmtCancelled->fetch(PDO::FETCH_ASSOC)['total_quality'];
    
        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        } else {
            echo json_encode([
                'status' => 1,
                'books' => $listbook,
                'total_quality' => [
                    'Đã giao' => $deliveredCount,
                    'Đã đặt' => $orderedCount,
                    'Đã hủy' => $cancelledCount
                ]
            ]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
    
}else if($method == "DELETE") {
    try {
        $idbill = $_GET['idbill'];

        // Kiểm tra xem username có được cung cấp không
        if (empty($idbill)) {
            echo json_encode(['status' => 0, 'msg' => 'Tên người dùng không hợp lệ']);
            exit();
        }

        $sql = "DELETE FROM bill WHERE idbill = :idbill";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':idbill', $idbill);

        // Thực thi câu truy vấn
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Xóa người dùng thành công']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Không thể xóa người dùng']);
        }

    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        $idbill = $data['idbill']; 
        $status = $data['status'];
        

        $sql = "UPDATE bill SET status = :status WHERE idbill = :idbill";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);      
        $stmt->bindParam(':idbill', $idbill, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>