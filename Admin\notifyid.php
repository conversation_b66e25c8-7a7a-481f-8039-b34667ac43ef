<?php
 error_reporting(E_ALL);
 ini_set("display_errors", 1);

 // Header cho phép truy cập từ mọi nguồn (CORS)
 header("Access-Control-Allow-Origin: *");
 header("Access-Control-Allow-Headers: Content-Type, Authorization");
 header("Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS"); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method == "GET") {
    try {
        $id = $_GET['id'];
        $sql = "SELECT * FROM notifycation WHERE id = :id";
        $stmt = $conn->prepare($sql);
        
        // Bind the id to the prepared statement
        $stmt->bindParam(':id', $id, PDO::PARAM_STR);
        
        $stmt->execute();
        $user = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($user)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có người dùng nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'user' => $user[0]]); // Return only the first user record
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method == "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        $id = $data['id']; 
        $title = $data['title'];
        $descript = $data['descript'];

        $sql = "UPDATE notifycation SET title = :title, descript = :descript WHERE id = :id";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':title', $title, PDO::PARAM_STR);
        $stmt->bindParam(':descript', $descript, PDO::PARAM_STR);
      
        $stmt->bindParam(':id', $id, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}
?>