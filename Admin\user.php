<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: *");
header('Content-Type: application/json; charset=UTF-8'); 

include '../DbConnect.php';
$onjDB = new DbConnect();
$conn = $onjDB->connect();

$method = $_SERVER['REQUEST_METHOD'];

if ($method === "GET") {
    try {
        $sql = "SELECT * FROM user";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $listbook = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($listbook)) {
            echo json_encode(['status' => 0, 'msg' => 'Không có sách nào trong danh sách.']);
        } else {
            echo json_encode(['status' => 1, 'books' => $listbook]);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}else if ($method === "POST") {
    try {

        $data = json_decode(file_get_contents("php://input"), true);
        
        $username = $data['username']; 
        $fullname = $data['fullname'];
        $phone = $data['phone'];
        $email = $data['email'];
        $pass = $data['pass'];

        $sql = "UPDATE user SET fullname = :fullname, phone = :phone, email = :email, pass = :pass WHERE username = :username";
        $stmt = $conn->prepare($sql);
        
        $stmt->bindParam(':fullname', $fullname, PDO::PARAM_STR);
        $stmt->bindParam(':phone', $phone, PDO::PARAM_STR);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':pass', $pass, PDO::PARAM_STR);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        
        if ($stmt->execute()) {
            echo json_encode(['status' => 1, 'msg' => 'Cập nhật người dùng thành công.']);
        } else {
            echo json_encode(['status' => 0, 'msg' => 'Cập nhật người dùng thất bại.']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 0, 'msg' => 'Lỗi truy vấn cơ sở dữ liệu: ' . $e->getMessage()]);
    }
}

?>